<ion-content class="ion-padding login-page">

  <ion-card>
    <ion-card-header>
      <ion-card-title class="ion-text-center">Login</ion-card-title>
    </ion-card-header>

    <ion-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">

        <ion-item>
          <ion-label position="floating">Email</ion-label>
          <ion-input formControlName="email" type="email"></ion-input>
        </ion-item>

        <ion-item>
          <ion-label position="floating">Password</ion-label>
          <ion-input [type]="hidePassword ? 'password' : 'text'" formControlName="password"></ion-input>
          <ion-icon name="{{hidePassword ? 'eye-off' : 'eye'}}" slot="end" (click)="togglePassword()"></ion-icon>
        </ion-item>

        <ion-item>
          <ion-label>Login As</ion-label>
          <ion-select formControlName="role" interface="action-sheet">
            <ion-select-option value="Admin">Admin</ion-select-option>
            <ion-select-option value="DeliveryBoy">Delivery Boy</ion-select-option>
          </ion-select>
        </ion-item>

        <ion-button expand="block" type="submit" [disabled]="loginForm.invalid">
          Login
        </ion-button>

      </form>
    </ion-card-content>
  </ion-card>
</ion-content>
