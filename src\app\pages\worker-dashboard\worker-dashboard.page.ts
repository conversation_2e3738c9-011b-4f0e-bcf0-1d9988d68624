import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';

@Component({
  selector: 'app-worker-dashboard',
  templateUrl: './worker-dashboard.page.html',
  styleUrls: ['./worker-dashboard.page.scss'],
  standalone: true,
  imports: [IonContent, IonHeader, IonTitle, IonToolbar, CommonModule, FormsModule]
})
export class WorkerDashboardPage implements OnInit {

  constructor() { }

  ngOnInit() {
  }

}
